[package]
name = "reforged-web"
version = "0.1.0"
edition = "2024"

[dependencies]
actix-web.workspace = true
rust-embed = "8.5.0"
actix-web-static-files = "4.0.1"
mime_guess.workspace = true
tracing.workspace = true
thiserror.workspace = true
serde.workspace = true
serde_json.workspace = true

[dev-dependencies]
tempfile = "3.13.0"
tracing-subscriber.workspace = true

[[example]]
name = "integration"
path = "examples/integration.rs"
