{"name": "@react-router/dev", "version": "7.6.3", "description": "Dev tools and CLI for React Router", "homepage": "https://reactrouter.com", "bugs": {"url": "https://github.com/remix-run/react-router/issues"}, "repository": {"type": "git", "url": "https://github.com/remix-run/react-router", "directory": "packages/react-router-dev"}, "license": "MIT", "exports": {"./config": {"types": "./dist/config.d.ts", "default": "./dist/config.js"}, "./routes": {"types": "./dist/routes.d.ts", "default": "./dist/routes.js"}, "./vite": {"types": "./dist/vite.d.ts", "default": "./dist/vite.js"}, "./vite/cloudflare": {"types": "./dist/vite/cloudflare.d.ts", "default": "./dist/vite/cloudflare.js"}, "./package.json": "./package.json"}, "imports": {"#module-sync-enabled": {"module-sync": "./module-sync-enabled/true.mjs", "default": "./module-sync-enabled/false.cjs"}}, "bin": {"react-router": "bin.js"}, "wireit": {"build": {"command": "tsup", "files": ["cli/**", "config/**", "module-sync-enabled/**", "typegen/**", "vite/**", "*.ts", "bin.js", "tsconfig.json", "package.json"], "output": ["dist/**"]}}, "dependencies": {"@babel/core": "^7.21.8", "@babel/generator": "^7.21.5", "@babel/parser": "^7.21.8", "@babel/plugin-syntax-decorators": "^7.22.10", "@babel/plugin-syntax-jsx": "^7.21.4", "@babel/preset-typescript": "^7.21.5", "@babel/traverse": "^7.23.2", "@babel/types": "^7.22.5", "@npmcli/package-json": "^4.0.1", "arg": "^5.0.1", "babel-dead-code-elimination": "^1.0.6", "chokidar": "^4.0.0", "dedent": "^1.5.3", "es-module-lexer": "^1.3.1", "exit-hook": "2.2.1", "jsesc": "3.0.2", "lodash": "^4.17.21", "pathe": "^1.1.2", "picocolors": "^1.1.1", "prettier": "^2.7.1", "react-refresh": "^0.14.0", "semver": "^7.3.7", "set-cookie-parser": "^2.6.0", "tinyglobby": "^0.2.14", "valibot": "^0.41.0", "vite-node": "^3.1.4", "@react-router/node": "7.6.3"}, "devDependencies": {"@types/babel__core": "^7.20.5", "@types/babel__generator": "^7.6.8", "@types/babel__traverse": "^7.20.5", "@types/dedent": "^0.7.0", "@types/express": "^4.17.9", "@types/jsesc": "^3.0.1", "@types/lodash": "^4.14.182", "@types/node": "^20.0.0", "@types/npmcli__package-json": "^4.0.0", "@types/prettier": "^2.7.3", "@types/set-cookie-parser": "^2.4.1", "esbuild-register": "^3.6.0", "execa": "5.1.1", "express": "^4.19.2", "fast-glob": "3.2.11", "tsup": "^8.3.0", "typescript": "^5.1.6", "vite": "^6.1.0", "wireit": "0.14.9", "wrangler": "^4.2.0", "@react-router/serve": "7.6.3", "react-router": "^7.6.3"}, "peerDependencies": {"typescript": "^5.1.0", "vite": "^5.1.0 || ^6.0.0 || ^7.0.0", "wrangler": "^3.28.2 || ^4.0.0", "@react-router/serve": "^7.6.3", "react-router": "^7.6.3"}, "peerDependenciesMeta": {"@react-router/serve": {"optional": true}, "typescript": {"optional": true}, "wrangler": {"optional": true}}, "engines": {"node": ">=20.0.0"}, "files": ["dist/", "module-sync-enabled/", "bin.js", "CHANGELOG.md", "LICENSE.md", "README.md"], "scripts": {"build": "wireit", "typecheck": "tsc"}}