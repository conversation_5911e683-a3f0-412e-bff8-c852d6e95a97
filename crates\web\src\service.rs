use actix_web::{
    App, HttpRequest, HttpResponse, Result as ActixResult,
    body::MessageBody,
    dev::{ServiceFactory, ServiceRequest, ServiceResponse},
    middleware::{Logger, NormalizePath},
    web::{self, Data},
    Error, HttpResponseBuilder,
};
use rust_embed::RustEmbed;
use tracing::{error, info};

use crate::{WebConfig, WebError};

/// Embedded frontend assets
#[derive(RustEmbed)]
#[folder = "frontend/dist/"]
pub struct FrontendAssets;

/// Creates the web service for serving the React application
pub fn create_web_service(
    config: WebConfig,
) -> App<
    impl ServiceFactory<
        ServiceRequest,
        Response = ServiceResponse<impl MessageBody>,
        Config = (),
        InitError = (),
        Error = Error,
    >,
> {
    info!("Setting up web service with embedded frontend assets");

    App::new()
        .wrap(Logger::default())
        .wrap(NormalizePath::trim())
        .app_data(Data::new(config))
        // Serve static assets (JS, CSS, images, etc.)
        .service(web::resource("/assets/{filename:.*}").route(web::get().to(serve_asset)))
        // Health check endpoint
        .route("/health", web::get().to(health_check))
        // Catch-all route for React Router (SPA support)
        .default_service(web::route().to(spa_handler))
}

/// Serve individual assets from embedded files
async fn serve_asset(path: web::Path<String>) -> ActixResult<HttpResponse, WebError> {
    let filename = path.into_inner();

    match FrontendAssets::get(&filename) {
        Some(content) => {
            let mime_type = mime_guess::from_path(&filename).first_or_octet_stream();
            Ok(HttpResponse::Ok()
                .content_type(mime_type.as_ref())
                .body(content.data.into_owned()))
        }
        None => Err(WebError::FileNotFound(format!("Asset not found: {}", filename)))
    }
}

/// Health check endpoint
async fn health_check() -> ActixResult<HttpResponse> {
    Ok(HttpResponse::Ok().json(serde_json::json!({
        "status": "healthy",
        "service": "reforged-web"
    })))
}

/// SPA handler that serves index.html for all unmatched routes
/// This enables client-side routing with React Router
async fn spa_handler(
    req: HttpRequest,
    _config: Data<WebConfig>,
) -> ActixResult<HttpResponse, WebError> {
    let path = req.path();

    // Don't serve index.html for API routes or other specific paths
    if path.starts_with("/api/") || path.starts_with("/ws") {
        return Err(WebError::FileNotFound(format!("Route not found: {}", path)));
    }

    // Try to serve index.html from embedded assets
    match FrontendAssets::get("index.html") {
        Some(content) => {
            info!("Serving SPA route '{}' with embedded index.html", path);
            Ok(HttpResponse::Ok()
                .content_type("text/html")
                .body(content.data.into_owned()))
        }
        None => {
            error!("index.html not found in embedded assets");
            Err(WebError::FileNotFound(
                "Frontend build not found. Please build the React application first.".to_string()
            ))
        }
    }
}

/// Utility function to check if the frontend build exists
pub fn validate_frontend_build(config: &WebConfig) -> Result<(), WebError> {
    let dist_path = &config.frontend_dist_path;
    let index_path = dist_path.join("index.html");
    
    if !dist_path.exists() {
        return Err(WebError::FileNotFound(format!(
            "Frontend dist directory not found: {:?}. Please build the React application first.",
            dist_path
        )));
    }
    
    if !index_path.exists() {
        return Err(WebError::FileNotFound(format!(
            "index.html not found in dist directory: {:?}. Please build the React application first.",
            index_path
        )));
    }
    
    info!("Frontend build validated at: {:?}", dist_path);
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::fs;
    use tempfile::TempDir;

    #[test]
    fn test_validate_frontend_build_missing_directory() {
        let config = WebConfig::new("/nonexistent/path");
        let result = validate_frontend_build(&config);
        assert!(result.is_err());
        assert!(matches!(result.unwrap_err(), WebError::FileNotFound(_)));
    }

    #[test]
    fn test_validate_frontend_build_missing_index() {
        let temp_dir = TempDir::new().unwrap();
        let config = WebConfig::new(temp_dir.path());
        let result = validate_frontend_build(&config);
        assert!(result.is_err());
        assert!(matches!(result.unwrap_err(), WebError::FileNotFound(_)));
    }

    #[test]
    fn test_validate_frontend_build_success() {
        let temp_dir = TempDir::new().unwrap();
        let index_path = temp_dir.path().join("index.html");
        fs::write(&index_path, "<html><body>Test</body></html>").unwrap();
        
        let config = WebConfig::new(temp_dir.path());
        let result = validate_frontend_build(&config);
        assert!(result.is_ok());
    }
}
