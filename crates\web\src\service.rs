use actix_files::{Files, NamedFile};
use actix_web::{
    App, HttpRequest, HttpResponse, Result as ActixResult,
    body::MessageBody,
    dev::{ServiceFactory, ServiceRequest, ServiceResponse},
    middleware::{Logger, NormalizePath},
    web::{self, Data},
    Error,
};
use tracing::{error, info};

use crate::{WebConfig, WebError};

/// Creates the web service for serving the React application
pub fn create_web_service(
    config: WebConfig,
) -> App<
    impl ServiceFactory<
        ServiceRequest,
        Response = ServiceResponse<impl MessageBody>,
        Config = (),
        InitError = (),
        Error = Error,
    >,
> {
    let frontend_path = config.frontend_dist_path.clone();
    
    info!("Setting up web service to serve frontend from: {:?}", frontend_path);
    
    App::new()
        .wrap(Logger::default())
        .wrap(NormalizePath::trim())
        .app_data(Data::new(config))
        // Serve static files (JS, CSS, images, etc.)
        .service(
            Files::new("/static", &frontend_path)
                .show_files_listing()
                .use_etag(true)
                .use_last_modified(true)
        )
        // Serve assets directory if it exists
        .service(
            Files::new("/assets", frontend_path.join("assets"))
                .show_files_listing()
                .use_etag(true)
                .use_last_modified(true)
        )
        // Health check endpoint
        .route("/health", web::get().to(health_check))
        // Catch-all route for React Router (SPA support)
        .default_service(web::route().to(spa_handler))
}

/// Health check endpoint
async fn health_check() -> ActixResult<HttpResponse> {
    Ok(HttpResponse::Ok().json(serde_json::json!({
        "status": "healthy",
        "service": "reforged-web"
    })))
}

/// SPA handler that serves index.html for all unmatched routes
/// This enables client-side routing with React Router
async fn spa_handler(
    req: HttpRequest,
    config: Data<WebConfig>,
) -> ActixResult<NamedFile, WebError> {
    let path = req.path();
    
    // Don't serve index.html for API routes or other specific paths
    if path.starts_with("/api/") || path.starts_with("/ws") {
        return Err(WebError::FileNotFound(format!("Route not found: {}", path)));
    }
    
    let index_path = config.frontend_dist_path.join("index.html");
    
    if !index_path.exists() {
        error!("index.html not found at: {:?}", index_path);
        return Err(WebError::FileNotFound(
            "Frontend build not found. Please build the React application first.".to_string()
        ));
    }
    
    info!("Serving SPA route '{}' with index.html", path);
    
    NamedFile::open(index_path)
        .map_err(|e| {
            error!("Failed to open index.html: {}", e);
            WebError::IoError(e)
        })
}

/// Utility function to check if the frontend build exists
pub fn validate_frontend_build(config: &WebConfig) -> Result<(), WebError> {
    let dist_path = &config.frontend_dist_path;
    let index_path = dist_path.join("index.html");
    
    if !dist_path.exists() {
        return Err(WebError::FileNotFound(format!(
            "Frontend dist directory not found: {:?}. Please build the React application first.",
            dist_path
        )));
    }
    
    if !index_path.exists() {
        return Err(WebError::FileNotFound(format!(
            "index.html not found in dist directory: {:?}. Please build the React application first.",
            index_path
        )));
    }
    
    info!("Frontend build validated at: {:?}", dist_path);
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::fs;
    use tempfile::TempDir;

    #[test]
    fn test_validate_frontend_build_missing_directory() {
        let config = WebConfig::new("/nonexistent/path");
        let result = validate_frontend_build(&config);
        assert!(result.is_err());
        assert!(matches!(result.unwrap_err(), WebError::FileNotFound(_)));
    }

    #[test]
    fn test_validate_frontend_build_missing_index() {
        let temp_dir = TempDir::new().unwrap();
        let config = WebConfig::new(temp_dir.path());
        let result = validate_frontend_build(&config);
        assert!(result.is_err());
        assert!(matches!(result.unwrap_err(), WebError::FileNotFound(_)));
    }

    #[test]
    fn test_validate_frontend_build_success() {
        let temp_dir = TempDir::new().unwrap();
        let index_path = temp_dir.path().join("index.html");
        fs::write(&index_path, "<html><body>Test</body></html>").unwrap();
        
        let config = WebConfig::new(temp_dir.path());
        let result = validate_frontend_build(&config);
        assert!(result.is_ok());
    }
}
