use actix_web::{HttpServer, middleware::Lo<PERSON>};
use reforged_web::{WebConfig, create_web_service, service::validate_frontend_build};
use std::path::PathBuf;
use tracing::info;

/// Example of how to integrate the web service with your existing Actix setup
#[actix_web::main]
async fn main() -> std::io::Result<()> {
    // Initialize logging
    tracing_subscriber::fmt::init();

    // Configure the web service
    let frontend_path = PathBuf::from("crates/web/frontend/dist");
    let web_config = WebConfig::new(&frontend_path)
        .with_dev_mode(true);

    // Validate that the frontend build exists
    if let Err(e) = validate_frontend_build(&web_config) {
        eprintln!("Frontend validation failed: {}", e);
        eprintln!("Please build your React application first:");
        eprintln!("  cd crates/web/frontend");
        eprintln!("  npm run build");
        std::process::exit(1);
    }

    info!("Starting web server on http://localhost:8080");

    // Start the server
    HttpServer::new(move || {
        create_web_service(web_config.clone())
    })
    .bind("127.0.0.1:8080")?
    .run()
    .await
}
