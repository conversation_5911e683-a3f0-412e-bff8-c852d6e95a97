use std::path::{Path, PathBuf};

pub mod error;
pub mod service;

pub use error::WebError;
pub use service::create_web_service;

/// Configuration for the web service
#[derive(Clone)]
pub struct WebConfig {
    /// Path to the frontend dist directory
    pub frontend_dist_path: PathBuf,
    /// Whether to enable development mode (more verbose logging)
    pub dev_mode: bool,
}

impl WebConfig {
    pub fn new<P: AsRef<Path>>(frontend_dist_path: P) -> Self {
        Self {
            frontend_dist_path: frontend_dist_path.as_ref().to_path_buf(),
            dev_mode: false,
        }
    }

    pub fn with_dev_mode(mut self, dev_mode: bool) -> Self {
        self.dev_mode = dev_mode;
        self
    }
}

impl Default for WebConfig {
    fn default() -> Self {
        Self::new("crates/web/frontend/dist")
    }
}
