use actix_web::{HttpResponse, ResponseError, http::StatusCode};
use serde_json::json;

#[derive(Debug, thiserror::Error)]
pub enum WebError {
    #[error("File not found: {0}")]
    FileNotFound(String),
    #[error("IO error: {0}")]
    IoError(#[from] std::io::Error),
    #[error("Internal server error: {0}")]
    InternalError(String),
}

impl ResponseError for WebError {
    fn status_code(&self) -> StatusCode {
        match self {
            WebError::FileNotFound(_) => StatusCode::NOT_FOUND,
            WebError::IoError(_) => StatusCode::INTERNAL_SERVER_ERROR,
            WebError::InternalError(_) => StatusCode::INTERNAL_SERVER_ERROR,
        }
    }

    fn error_response(&self) -> HttpResponse {
        let cause = match self {
            WebError::FileNotFound(_) => "FILE_NOT_FOUND",
            WebError::IoError(_) => "IO_ERROR",
            WebError::InternalError(_) => "INTERNAL_ERROR",
        };

        let json_error = json!({
            "cause": cause,
            "error": self.to_string(),
        });

        HttpResponse::build(self.status_code()).json(json_error)
    }
}
