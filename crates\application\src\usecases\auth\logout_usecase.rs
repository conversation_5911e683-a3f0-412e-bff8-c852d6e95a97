use std::sync::Arc;

use chrono::{DateTime, Utc};
use serde::Serialize;

use crate::{
    error::ApplicationError,
    traits::{PasetoClaimPurpose, TokenService},
};

#[derive(Serialize)]
pub struct LogoutResponse {
    logged_out_at: DateTime<Utc>,
}

pub struct LogoutUsecase {
    token_service: Arc<dyn TokenService>,
}

impl LogoutUsecase {
    pub fn new(token_service: Arc<dyn TokenService>) -> Self {
        Self { token_service }
    }
}

impl LogoutUsecase {
    pub async fn execute(&self, refresh_token: String) -> Result<LogoutResponse, ApplicationError> {
        let token_service = self.token_service.clone();

        let _ = token_service.validate_token(refresh_token, PasetoClaimPurpose::RefreshToken)?;

        // TODO: remove the session in the database using the session id from refresh token

        let now = Utc::now();
        let response = LogoutResponse { logged_out_at: now };

        Ok(response)
    }
}
