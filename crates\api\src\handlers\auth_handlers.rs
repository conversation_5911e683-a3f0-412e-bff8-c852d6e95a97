use actix_web::{
    HttpRequest, HttpResponse, Sc<PERSON>,
    body::BoxBody,
    cookie::{<PERSON><PERSON>, time::Duration},
    web::{self, <PERSON>, <PERSON><PERSON>, Path, get, post},
};
use reforged_application::{
    error::ApplicationError,
    usecases::{
        auth::{
            login_usecase::LoginUsecase,
            logout_usecase::LogoutUsecase,
            refresh_token_usecase::{RefreshTokenResponse, RefreshTokenUsecase},
            token_verification_usecase::{TokenVerificationResponse, TokenVerificationUsecase},
        },
        password_reset::{
            forgot_password_usecase::{ForgotPasswordResponse, ForgotPasswordUsecase},
            reset_password_usecase::{ResetPasswordResponse, ResetPasswordUsecase},
        },
    },
};

use crate::{
    dtos::{
        auth_dtos::{LogoutDTO, RefreshTokenDTO},
        user_dtos::{ForgotPasswordDTO, LoginDTO, ResetPasswordDTO},
        validate::Validate<PERSON><PERSON>,
    },
    error::ApiError,
    state::ApiState,
};

pub fn create_auth_service() -> Scope {
    web::scope("/auth")
        .service(web::resource("/login").route(post().to(login_user_handler)))
        .service(web::resource("/refresh").route(post().to(refresh_token_handler)))
        .service(web::resource("/validate/{token}").route(get().to(token_verification_handler)))
        .service(web::resource("/logout").route(post().to(logout_handler)))
        .service(web::resource("/forgot-password").route(post().to(forgot_password_handler)))
        .service(web::resource("/reset-password").route(post().to(reset_password_handler)))
}

async fn refresh_token_handler(
    req: HttpRequest,
    state: Data<ApiState>,
    ValidateJson(dto): ValidateJson<RefreshTokenDTO>,
) -> Result<Json<RefreshTokenResponse>, ApiError> {
    let usecase = RefreshTokenUsecase::new(state.token_service().clone());
    let cookies = req
        .cookies()
        .map_err(|e| ApiError::Application(ApplicationError::EntityNotFound(e.to_string())))?;
    let refresh_token = cookies.iter().find(|c| c.name() == "reforged-auth-refresh");

    let refresh_token = if let Some(refresh_token) = refresh_token {
        refresh_token.value().to_string()
    } else {
        dto.refresh_token.unwrap_or_default()
    };

    let response = usecase.execute(refresh_token).await?;

    Ok(Json(response))
}

async fn token_verification_handler(
    state: Data<ApiState>,
    token: Path<String>,
) -> Result<Json<TokenVerificationResponse>, ApiError> {
    let token = token.into_inner();
    let usecase = TokenVerificationUsecase::new(state.token_service().clone());

    let response = usecase.execute(token).await?;

    Ok(Json(response))
}

async fn logout_handler(
    req: HttpRequest,
    state: Data<ApiState>,
    ValidateJson(dto): ValidateJson<LogoutDTO>,
) -> Result<HttpResponse<BoxBody>, ApiError> {
    let usecase = LogoutUsecase::new(state.token_service().clone());

    let cookies = req
        .cookies()
        .map_err(|e| ApiError::Application(ApplicationError::EntityNotFound(e.to_string())))?;
    let refresh_token = cookies.iter().find(|c| c.name() == "reforged-auth-refresh");

    let refresh_token = if let Some(refresh_token) = refresh_token {
        refresh_token.value().to_string()
    } else {
        dto.refresh_token.unwrap_or_default()
    };

    let response = usecase.execute(refresh_token).await?;

    let cookie = Cookie::build("reforged-auth-refresh", "")
        .max_age(Duration::seconds(0))
        .http_only(true)
        .secure(true)
        .same_site(actix_web::cookie::SameSite::Lax)
        .finish();

    Ok(HttpResponse::Ok().cookie(cookie).json(response))
}

async fn login_user_handler(
    state: Data<ApiState>,
    ValidateJson(login): ValidateJson<LoginDTO>,
) -> Result<HttpResponse<BoxBody>, ApiError> {
    let login_usecase = LoginUsecase::new(
        state.captcha_service().clone(),
        state.password_hasher().clone(),
        state.user_query_handler().clone(),
        state.token_service().clone(),
    );

    let login_response = login_usecase
        .execute(login.username, login.password, login.captcha)
        .await?;

    let refresh_token = &login_response.refresh_token;
    let refresh_token_expiry = &login_response.refresh_token_expiration;
    let max_age = refresh_token_expiry.clone() - chrono::Utc::now();
    let max_age_duration = max_age.num_seconds();
    let max_age_duration = Duration::seconds(max_age_duration);

    let cookie = Cookie::build("reforged-auth-refresh", refresh_token)
        .max_age(max_age_duration)
        .http_only(true)
        .secure(true)
        .same_site(actix_web::cookie::SameSite::Lax)
        .finish();

    Ok(HttpResponse::Ok().cookie(cookie).json(login_response))
}

async fn forgot_password_handler(
    state: Data<ApiState>,
    ValidateJson(dto): ValidateJson<ForgotPasswordDTO>,
) -> Result<Json<ForgotPasswordResponse>, ApiError> {
    let forgot_password_usecase = ForgotPasswordUsecase::new(
        state.captcha_service().clone(),
        state.user_query_handler().clone(),
        state.token_service().clone(),
        state.user_store_manager().clone(),
    );

    let forgot_password_response = forgot_password_usecase
        .execute(dto.email, dto.captcha)
        .await?;

    Ok(Json(forgot_password_response))
}

async fn reset_password_handler(
    state: Data<ApiState>,
    ValidateJson(dto): ValidateJson<ResetPasswordDTO>,
) -> Result<Json<ResetPasswordResponse>, ApiError> {
    let reset_password_usecase = ResetPasswordUsecase::new(
        state.captcha_service().clone(),
        state.token_service().clone(),
        state.user_query_handler().clone(),
        state.password_hasher().clone(),
        state.password_reset_token_query_handler().clone(),
        state.user_store_manager().clone(),
    );

    let response = reset_password_usecase
        .execute(dto.token, dto.new_password, dto.captcha)
        .await?;

    Ok(Json(response))
}
