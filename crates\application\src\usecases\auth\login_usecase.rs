use std::sync::Arc;

use chrono::{DateTime, Duration, Utc};
use reforged_domain::traits::password_hasher::PasswordHasher;
use serde::Serialize;

use crate::{
    error::ApplicationError,
    queries::{
        user::handler::{User<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, UserResponse},
        user::queries::GetUserByUsernameQuery,
    },
    services::captcha_service::Captcha,
    traits::{PasetoClaimPurpose, PasetoClaims, QueryHandler, TokenService},
};

#[derive(Serialize)]
pub struct LoginResponse {
    pub user: UserResponse,
    pub access_token: String,
    pub refresh_token: String,
    pub access_token_expiration: DateTime<Utc>,
    pub refresh_token_expiration: DateTime<Utc>,
}

impl LoginResponse {
    pub fn new(
        user: UserResponse,
        access_token: String,
        refresh_token: String,
        access_token_expiration: DateTime<Utc>,
        refresh_token_expiration: DateTime<Utc>,
    ) -> Self {
        Self {
            user,
            access_token,
            refresh_token,
            access_token_expiration,
            refresh_token_expiration,
        }
    }
}

pub struct LoginUsecase {
    captcha_service: Arc<dyn Captcha>,
    password_hasher: Arc<dyn PasswordHasher>,
    token_service: Arc<dyn TokenService>,
    user_query_handler: UserQueryHandler,
}

impl LoginUsecase {
    pub fn new(
        captcha_service: Arc<dyn Captcha>,
        password_hasher: Arc<dyn PasswordHasher>,
        user_query_handler: UserQueryHandler,
        token_service: Arc<dyn TokenService>,
    ) -> Self {
        Self {
            captcha_service,
            password_hasher,
            user_query_handler,
            token_service,
        }
    }
}

impl LoginUsecase {
    pub async fn execute(
        &self,
        username: String,
        password: String,
        captcha: String,
    ) -> Result<LoginResponse, ApplicationError> {
        self.captcha_service.validate(captcha).await?;

        let password_hasher = self.password_hasher.clone();

        let check_user_query = GetUserByUsernameQuery {
            username: username.clone(),
        };

        let check_user = self.user_query_handler.handle(check_user_query).await?;

        if check_user.is_none() {
            return Err(ApplicationError::UserNotFound);
        }

        let user = check_user.unwrap();

        password_hasher
            .verify(&password, &user.hashed_password)
            .await
            .map_err(|_| ApplicationError::InvalidCredentials)?;

        let token_service = self.token_service.clone();

        let access_token_claims = PasetoClaims::new(
            user.id,
            user.username.to_owned(),
            user.email.to_owned(),
            Duration::seconds(30),
            PasetoClaimPurpose::AccessToken,
        );

        let refresh_token_claims = PasetoClaims::new(
            user.id,
            user.username.to_owned(),
            user.email.to_owned(),
            Duration::days(30),
            PasetoClaimPurpose::RefreshToken,
        );

        let (access_token, access_token_expiration) =
            token_service.generate_token(access_token_claims, Duration::seconds(30))?;
        let (refresh_token, refresh_token_expiration) =
            token_service.generate_token(refresh_token_claims, Duration::days(30))?;

        Ok(LoginResponse::new(
            user,
            access_token,
            refresh_token,
            access_token_expiration,
            refresh_token_expiration,
        ))
    }
}
