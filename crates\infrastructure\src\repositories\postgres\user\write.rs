use chrono::Utc;
use reforged_domain::models::profile::value_object::Gender;
use reforged_domain::models::user::value_object::UserId;
use reforged_domain::{
    error::RepositoryError, models::user::entity::User, repository::user_repository::UserRepository,
};
use reforged_shared::uuid_generator::{uuid_from_str, uuid_now};
use reforged_shared::{IdTrait, Value};
use sea_orm::ActiveValue::Set;
use sea_orm::prelude::DateTime;
use sea_orm::sqlx::types::uuid::{self};
use sea_orm::{ActiveModelTrait, DbErr, IntoActiveModel, TransactionError, TransactionTrait};
use sea_orm::{DatabaseConnection, EntityTrait, prelude::async_trait::async_trait};

use crate::SeaORMErr;
use crate::models::sea_orm_active_enums;

use crate::models::profiles::ActiveModel as ProfilesActiveModel;
use crate::models::user_colors::ActiveModel as UserColorsActiveModel;
use crate::models::user_currencies::ActiveModel as UserCurrenciesActiveModel;
use crate::models::user_exps::ActiveModel as UserExpsActiveModel;
use crate::models::user_items::ActiveModel as UserItemsActiveModel;
use crate::models::user_quests::ActiveModel as UserQuestsActiveModel;
use crate::models::user_slots::ActiveModel as UserSlotsActiveModel;
use crate::models::user_titles::ActiveModel as UserTitlesActiveModel;
use crate::models::users::ActiveModel as UsersActiveModel;
use crate::persistence::models::prelude::*;

#[allow(dead_code)]
pub struct PostgresUserRepository {
    pool: DatabaseConnection,
}

impl PostgresUserRepository {
    pub fn new(pool: DatabaseConnection) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl UserRepository for PostgresUserRepository {
    async fn save(&self, user: &User, gender: &Gender) -> Result<(), RepositoryError> {
        // converted to unix timestamp since epoch
        let starter_item_ids = [
            "0196ba87-e698-771d-b7fb-34bdeea0b68e",
            "0196ba87-e697-763b-a8f7-a6e083855d02",
            "0196ba87-e697-763b-a8f7-af97b4c12d19",
            "0196ba87-e698-771d-b7fa-c55d98cbbe70",
        ];

        let gender = gender.clone();
        self.pool
            .transaction::<_, (), DbErr>(|txn| {
                let user = user.clone();

                Box::pin(async move {
                    let user = user.clone();
                    let now = Utc::now().naive_utc();
                    let user_id = user.id().get_id();
                    let role_id = uuid_from_str("0196ba87-e68b-743d-9105-4fc1aea15e55").unwrap();
                    let title_id = uuid_from_str("0196ba87-e6b5-7763-b97d-4faa5a1cc82f").unwrap();

                    let user_model = UsersActiveModel {
                        id: Set(user_id),
                        username: Set(user.username().value()),
                        email: Set(user.email().value()),
                        hash: Set(user.hashed_password().hash().to_owned()),
                        salt: Set(user.hashed_password().salt().to_owned()),
                        role_id: Set(role_id),
                        title_id: Set(title_id),
                        last_login: Set(now),
                        date_created: Set(now),
                    }
                    .insert(txn)
                    .await?;

                    let user_id = user_model.id;

                    let gender = match gender {
                        Gender::Male => sea_orm_active_enums::Gender::M,
                        Gender::Female => sea_orm_active_enums::Gender::F,
                    };

                    ProfilesActiveModel {
                        id: Set(uuid_now()),
                        user_id: Set(user_id),
                        age: Set(1),
                        gender: Set(gender),
                        avatar: Set("".to_owned()),
                        country: Set("".to_owned()),
                    }
                    .insert(txn)
                    .await?;

                    UserCurrenciesActiveModel {
                        id: Set(uuid_now()),
                        user_id: Set(user_id),
                        gold: Set(0),
                        diamonds: Set(0),
                        coins: Set(0),
                        crystal: Set(0),
                    }
                    .insert(txn)
                    .await?;

                    UserExpsActiveModel {
                        id: Set(uuid_now()),
                        user_id: Set(user_id),
                        exp: Set(0),
                        level: Set(1),
                    }
                    .insert(txn)
                    .await?;

                    for item_id in starter_item_ids {
                        let item_id = uuid_from_str(item_id).unwrap();
                        let item = create_default_item(user_id, item_id);

                        item.insert(txn).await?;
                    }

                    UserTitlesActiveModel {
                        id: Set(uuid_now()),
                        user_id: Set(user_id),
                        title_id: Set(title_id),
                        date: Set(DateTime::default()),
                    }
                    .insert(txn)
                    .await?;

                    UserColorsActiveModel {
                        id: Set(uuid_now()),
                        user_id: Set(user_id),
                        color_chat: Set("0x000000".to_string()),
                        color_name: Set("0x295F98".to_string()),
                        color_hair: Set("0x000000".to_string()),
                        color_accessory: Set("0x800000".to_string()),
                        color_base: Set("0x2F3645".to_string()),
                        color_eye: Set("0x102C57".to_string()),
                        color_skin: Set("0xFFDBB5".to_string()),
                        color_trim: Set("0x6C4E31".to_string()),
                    }
                    .insert(txn)
                    .await?;

                    UserQuestsActiveModel {
                        id: Set(uuid_now()),
                        user_id: Set(user_id),
                        quests1: Set("0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000".into()),
                        quests2: Set("0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000".into()),
                        quests3: Set("0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000".into()),
                        quests4: Set("0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000".into()),
                        quests5: Set("0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000".into()),
                        daily_quests0: Set(0),
                        daily_quests1: Set(0),
                        daily_quests2: Set(0),
                        monthly_quests0: Set(0),
                        daily_ads: Set(0),
                    }
                    .insert(txn)
                    .await?;

                    UserSlotsActiveModel {
                        id: Set(uuid_now()),
                        user_id: Set(user_id),
                        slots_bag: Set(100),
                        slots_auction: Set(100),
                        slots_bank: Set(100),
                        slots_house: Set(100),
                    }
                    .insert(txn)
                    .await?;

                    Ok(())
                })
            })
            .await
            .map_err(|e| match e {
                TransactionError::Connection(e) => SeaORMErr::from(e),
                TransactionError::Transaction(e) => SeaORMErr::from(e),
            })?;

        Ok(())
    }

    async fn update(&self, user: &User) -> Result<(), RepositoryError> {
        let model = Users::find_by_id(user.id().get_id())
            .one(&self.pool)
            .await
            .map_err(SeaORMErr::from)?
            .ok_or(RepositoryError::NotFound(format!(
                "with id {}",
                user.id().get_id()
            )))?;

        let mut active_model = model.into_active_model();

        if !user.username().value().is_empty() {
            active_model.username = Set(user.username().value());
        }

        if !user.email().value().is_empty() {
            active_model.email = Set(user.email().value());
        }

        if !user.hashed_password().hash().is_empty() {
            active_model.hash = Set(user.hashed_password().hash().to_owned());
        }

        if !user.hashed_password().salt().is_empty() {
            active_model.salt = Set(user.hashed_password().salt().to_owned());
        }

        active_model
            .update(&self.pool)
            .await
            .map_err(SeaORMErr::from)?;

        Ok(())
    }

    async fn delete(&self, id: &UserId) -> Result<(), RepositoryError> {
        let model = Users::delete_by_id(id.get_id())
            .exec(&self.pool)
            .await
            .map_err(SeaORMErr::from)?;

        if model.rows_affected == 0 {
            return Err(RepositoryError::NotFound(format!("with id {}", id)));
        }

        Ok(())
    }
}

fn create_default_item(user_id: uuid::Uuid, item_id: uuid::Uuid) -> UserItemsActiveModel {
    let now = Utc::now().naive_utc();
    let enh_id = uuid_from_str("0196ba87-e691-7757-a7dd-b3fcba14451c").unwrap();

    UserItemsActiveModel {
        id: Set(uuid_now()),
        item_id: Set(item_id),
        user_id: Set(user_id),
        quantity: Set(1),
        enh_id: Set(enh_id),
        equipped: Set(1),
        bank: Set(0),
        date_purchased: Set(now),
        bind: Set(Some(0)),
    }
}
